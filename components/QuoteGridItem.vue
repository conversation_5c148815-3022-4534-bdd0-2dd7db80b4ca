<template>
  <div
    class="quote-grid-item group relative bg-white dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 p-6 transition-all duration-300 hover:shadow-lg hover:border-gray-400 dark:hover:border-gray-500 cursor-pointer h-full flex flex-col"
    @click="navigateToQuote"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <!-- Author and Reference Info (Top) -->
    <div class="flex items-center justify-between mb-4 flex-shrink-0">
      <div class="flex items-center gap-2 min-w-0 flex-1">
        <!-- Author Name -->
        <span
          v-if="quote.author"
          class="font-medium text-gray-900 dark:text-gray-100 truncate transition-opacity duration-300"
          :class="{ 'group-hover:opacity-100': true, 'opacity-100': !isHovered, 'opacity-0': isHovered }"
        >
          {{ quote.author.name }}
        </span>
        <span
          v-else
          class="font-medium text-gray-500 dark:text-gray-400 truncate transition-opacity duration-300"
          :class="{ 'group-hover:opacity-100': true, 'opacity-100': !isHovered, 'opacity-0': isHovered }"
        >
          Unknown Author
        </span>
        
        <!-- Book Icon (if reference exists) -->
        <UIcon
          v-if="quote.reference"
          name="i-ph-book"
          class="w-4 h-4 text-gray-600 dark:text-gray-400 flex-shrink-0"
          :title="`From: ${quote.reference.name}`"
        />
      </div>
    </div>

    <!-- Quote Content (Main) -->
    <div class="flex-1 flex items-center justify-center">
      <!-- Default State: Quote Content -->
      <blockquote
        class="text-center font-serif text-gray-800 dark:text-gray-200 leading-relaxed transition-opacity duration-300"
        :class="{ 
          'opacity-100': !isHovered, 
          'opacity-0': isHovered,
          'text-sm': quote.name.length > 200,
          'text-base': quote.name.length <= 200 && quote.name.length > 100,
          'text-lg': quote.name.length <= 100
        }"
      >
        "{{ quote.name }}"
      </blockquote>

      <!-- Hover State: Author Name -->
      <div
        class="absolute inset-0 flex items-center justify-center transition-opacity duration-300"
        :class="{ 'opacity-0': !isHovered, 'opacity-100': isHovered }"
      >
        <div class="text-center">
          <div
            v-if="quote.author"
            class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2"
          >
            {{ quote.author.name }}
          </div>
          <div
            v-else
            class="text-xl font-semibold text-gray-500 dark:text-gray-400 mb-2"
          >
            Unknown Author
          </div>
          
          <!-- Reference info on hover -->
          <div
            v-if="quote.reference"
            class="text-sm text-gray-600 dark:text-gray-400"
          >
            from "{{ quote.reference.name }}"
          </div>
        </div>
      </div>
    </div>

    <!-- Featured Badge (if featured) -->
    <UBadge
      v-if="quote.is_featured"
      color="yellow"
      variant="subtle"
      size="xs"
      class="absolute top-2 right-2"
    >
      Featured
    </UBadge>
  </div>
</template>

<script setup>
const props = defineProps({
  quote: {
    type: Object,
    required: true
  }
})

// Hover state management
const isHovered = ref(false)

// Navigation
const navigateToQuote = () => {
  navigateTo(`/quote/${props.quote.id}`)
}


</script>
