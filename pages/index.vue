<template>
  <div class="min-h-screen">
    <header class="p-8">
      <h1 class="font-title text-size-82 font-600 text-center line-height-none uppercase">Verbatims</h1>
    </header>

    <HomeEmptyView
      v-if="stats.quotes === 0 || needsOnboarding"
      :needs-onboarding="needsOnboarding"
      :onboarding-status="onboardingStatus"
      :stats="stats"
      @open-submit-modal="openSubmitModal"
    />

    <!-- Quotes Grid (when quotes exist) -->
    <div v-else class="px-8 pb-16">
      <!-- Stats -->
      <div class="max-w-lg font-serif mb-8">
        <span class="block text-gray-600 dark:text-gray-400">
          Showing {{ allQuotes.length }} of {{ stats.quotes || 0 }} quotes
        </span>
      </div>

      <!-- Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
        <QuoteGridItem
          v-for="quote in allQuotes"
          :key="quote.id"
          :quote="quote"
        />
      </div>

      <!-- Load More Button -->
      <div v-if="hasMore" class="text-center">
        <UButton
          @click="loadMore"
          :loading="loadingMore"
          :disabled="loadingMore"
          size="lg"
          variant="outline"
          class="px-8 py-4"
        >
          {{ loadingMore ? 'Loading...' : 'Load More Quotes' }}
        </UButton>
      </div>
    </div>

    <!-- Submit Quote Modal -->
    <SubmitQuoteDialog v-model="showSubmitModal" @submitted="refreshQuotes" />
  </div>
</template>

<script setup>
// SEO
useHead({
  title: 'Verbatims • Universal Quotes',
  meta: [
    { name: 'description', content: 'Discover inspiring quotes from authors, films, books, and more. A comprehensive, user-generated quotes database with moderation capabilities.' }
  ]
})

// Data fetching
const { data: quotesData } = await useFetch('/api/quotes', {
  query: { limit: 25, status: 'approved' }
})

const { data: statsData } = await useFetch('/api/stats')
const { data: featuredData } = await useFetch('/api/quotes/featured')
const { data: onboardingData } = await useFetch('/api/onboarding/status')

// Reactive state
const showSubmitModal = ref(false)
const displayedQuotes = ref([...(quotesData.value?.data || [])])
const hasMore = ref(quotesData.value?.pagination?.hasMore || false)
const loadingMore = ref(false)
const currentPage = ref(1)

// Computed
const stats = computed(() => statsData.value?.data || { quotes: 0, authors: 0, references: 0, users: 0 })
const featuredQuote = computed(() => featuredData.value?.data)
const onboardingStatus = computed(() => onboardingData.value?.data)
const needsOnboarding = computed(() => onboardingStatus.value?.needsOnboarding || false)

// Use displayed quotes directly for now (simplified)
const allQuotes = computed(() => displayedQuotes.value || [])

// Methods
const openSubmitModal = () => {
  showSubmitModal.value = true
}

const loadMore = async () => {
  if (loadingMore.value || !hasMore.value) return

  loadingMore.value = true
  try {
    const nextPage = currentPage.value + 1
    const { data } = await $fetch('/api/quotes', {
      query: {
        limit: 25,
        status: 'approved',
        page: nextPage
      }
    })

    if (data?.data) {
      displayedQuotes.value = [...displayedQuotes.value, ...data.data]
      hasMore.value = data.pagination?.hasMore || false
      currentPage.value = nextPage
    }
  } catch (error) {
    console.error('Failed to load more quotes:', error)
  } finally {
    loadingMore.value = false
  }
}

// Watch for new quotes from modal submission
const refreshQuotes = async () => {
  try {
    const { data } = await $fetch('/api/quotes', {
      query: { limit: 25, status: 'approved' }
    })
    displayedQuotes.value = [...(data?.data || [])]
    hasMore.value = data?.pagination?.hasMore || false
    currentPage.value = 1
  } catch (error) {
    console.error('Failed to refresh quotes:', error)
  }
}

// Refresh quotes when modal closes (in case new quote was submitted)
watch(showSubmitModal, (newValue, oldValue) => {
  if (oldValue && !newValue) {
    // Modal was closed, refresh quotes
    refreshQuotes()
  }
})
</script>